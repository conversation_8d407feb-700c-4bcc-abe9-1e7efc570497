'use client';

import { useMemo } from 'react';

interface DynamicContentHeaderProps {
  contentType: 'exercise' | 'homework' | 'summary' | 'exam';
  lessonTitle?: string;
  subjectName?: string;
  yearName?: string;
  levelName?: string;
}

export const DynamicContentHeader = ({
  contentType,
  lessonTitle,
  subjectName,
  yearName,
  levelName
}: DynamicContentHeaderProps) => {
  
  const contentData = useMemo(() => {
    const currentYear = new Date().getFullYear();
    const academicYear = `${currentYear}-${currentYear + 1}`;
    
    const baseContent = {
      exercise: {
        title: `تمارين ${lessonTitle || 'الدرس'}`,
        description: `تمارين محلولة ومصححة`,
        action: 'حل وتطبيق',
        icon: '📝',
        keywords: ['تمارين', 'حلول', 'تصحيح', 'تطبيقات', 'دروس', 'مسائل', 'أنشطة', 'تدريبات']
      },
      homework: {
        title: `فروض ${lessonTitle || 'الدرس'}`,
        description: `فروض منزلية محلولة`,
        action: 'إنجاز وحل',
        icon: '📚',
        keywords: ['فروض', 'فروض منزلية', 'تمارين منزلية', 'أنشطة', 'مراجعة', 'تطبيقات']
      },
      summary: {
        title: `ملخصات ${lessonTitle || 'الدرس'}`,
        description: `ملخصات شاملة ومركزة`,
        action: 'مراجعة ودراسة',
        icon: '📄',
        keywords: ['ملخصات', 'مراجعة', 'دروس', 'خلاصة', 'تلخيص', 'قواعد', 'نظريات', 'مفاهيم']
      },
      exam: {
        title: `امتحانات ${lessonTitle || 'الدرس'}`,
        description: `امتحانات وطنية وجهوية محلولة`,
        action: 'تحضير واختبار',
        icon: '🎯',
        keywords: ['امتحانات', 'امتحانات وطنية', 'امتحانات جهوية', 'اختبارات', 'فروض محروسة', 'تقييم', 'نماذج', 'باك']
      }
    };

    const content = baseContent[contentType];
    
    // Generate focused content based on type (~500 words)
    const generateFocusedText = () => {
      const baseText = `${content.description} ${subjectName ? `في مادة ${subjectName}` : ''} ${yearName ? `للسنة ${yearName}` : ''} ${levelName ? `- ${levelName}` : ''}`;

      switch (contentType) {
        case 'exercise':
          return `${baseText}. تمارين متنوعة ومتدرجة الصعوبة مع حلول وشروحات واضحة لتقوية فهم التلاميذ وتحسين مستواهم الدراسي. التمارين المتوفرة تشمل حلولاً مفصلة بطريقة واضحة ومبسطة تساعد على فهم الطرق المختلفة للحل.

تتضمن هذه المجموعة تمارين تطبيقية متنوعة تغطي جوانب مختلفة من المنهج الدراسي، من التمارين الأساسية إلى المتقدمة التي تتطلب مهارات التحليل والتركيب. التمارين المحلولة مرفقة بحلول مفصلة توضح خطوات الحل بطريقة منهجية، مما يساعد التلاميذ على تطوير قدراتهم في حل المسائل الرياضية والعلمية.

تم تصميم التمارين لتناسب مختلف مستويات التلاميذ، حيث تبدأ بتمارين بسيطة لترسيخ الأساسيات، ثم تتدرج في الصعوبة لتشمل تمارين تحدي تنمي مهارات التفكير النقدي. كما تتضمن تمارين تطبيقية من الحياة اليومية لربط المفاهيم النظرية بالواقع العملي. الحلول المتوفرة تتميز بالوضوح والدقة مع شرح الخطوات بالتفصيل وذكر القوانين المستخدمة.`;

        case 'homework':
          return `${baseText}. فروض منزلية متنوعة تساعد التلاميذ على المراجعة المستمرة والتحضير الفعال للامتحانات والفروض المحروسة. الفروض المتوفرة تتضمن حلولاً مفصلة وتصحيحاً مع شرح الأخطاء الشائعة وطرق تجنبها.

هذه الفروض المنزلية مصممة بعناية لتغطي المحاور والمفاهيم المهمة في المنهج الدراسي، وتهدف إلى تعزيز فهم التلاميذ وترسيخ المعلومات من خلال التطبيق العملي والممارسة المستمرة. الفروض تحتوي على مجموعة متنوعة من الأسئلة التي تتدرج في الصعوبة، من الأسئلة الأساسية إلى المتقدمة التي تتطلب التحليل والتطبيق.

تتميز هذه الفروض بأنها تحاكي نمط الامتحانات الرسمية والفروض المحروسة، مما يساعد التلاميذ على التعود على أسلوب الأسئلة وطريقة الإجابة المطلوبة. تتضمن أسئلة متنوعة مثل الأسئلة الموضوعية والمقالية والاختيار من متعدد، مما يوفر تدريباً على أنواع مختلفة من الأسئلة. الحلول المتوفرة شاملة ومفصلة مع شرح الأخطاء الشائعة ونصائح لتجنبها.`;

        case 'summary':
          return `${baseText}. ملخصات شاملة ومركزة تغطي جميع النقاط المهمة والمفاهيم الأساسية في الدرس، مصممة لتوفير مراجعة سريعة وفعالة للمفاهيم والقواعد والنظريات المهمة. هذه الملخصات تساعد التلاميذ على استيعاب المعلومات بطريقة منظمة ومنطقية.

تم إعداد هذه الملخصات بطريقة علمية ومنهجية لتضمن تغطية شاملة لجميع عناصر المنهج الدراسي، مع التركيز على النقاط الأساسية والمفاهيم الجوهرية. كل ملخص منظم بطريقة هرمية تبدأ بالمفاهيم العامة ثم تتدرج إلى التفاصيل الدقيقة، مما يساعد على الفهم التدريجي والمنطقي للمادة.

تتضمن الملخصات جداول ومخططات توضيحية تساعد على تبسيط المعلومات المعقدة وتقديمها بطريقة بصرية جذابة وسهلة الفهم. كما تحتوي على قوائم بالنقاط المهمة والقوانين الأساسية، مما يجعلها مرجعاً سريعاً للمراجعة قبل الامتحانات. هذه الملخصات مفيدة للتحضير للامتحانات وتعزيز الفهم وتنظيم المعلومات وربط المفاهيم المختلفة.`;

        case 'exam':
          return `${baseText}. نماذج امتحانات وطنية وجهوية مع تصحيح وشرح للأسئلة والحلول المتوفرة. تحضير مفيد للامتحانات الرسمية والفروض المحروسة، مع تدريب على أنماط الأسئلة المختلفة وطرق الإجابة الصحيحة.

هذه المجموعة تضم نماذج من الامتحانات الوطنية والجهوية للسنوات السابقة، مما يعطي التلاميذ فكرة واضحة عن طبيعة الأسئلة المتوقعة ومستوى الصعوبة المطلوب. الامتحانات المتوفرة مرفقة بتصحيح وشروحات تساعد على فهم طريقة التفكير المطلوبة للوصول إلى الإجابة الصحيحة.

تتنوع هذه النماذج لتشمل امتحانات من مختلف الجهات والأكاديميات، مما يساعد التلاميذ على التعرف على أنماط الأسئلة وأساليب الصياغة المختلفة. كما تتضمن امتحانات الدورة العادية والاستدراكية، مما يوفر تدريباً مفيداً للتحضير. التصحيح المتوفر يتميز بالوضوح مع شرح الأسئلة المحلولة بالتفصيل وذكر معايير التنقيط ونصائح لإدارة الوقت وتنظيم الإجابات.`;

        default:
          return baseText;
      }
    };

    return {
      ...content,
      fullDescription: `${content.description} في ${subjectName || 'المادة'} للسنة الدراسية ${academicYear}`,
      fullText: generateFocusedText(),
      structuredData: {
        '@context': 'https://schema.org',
        '@type': 'EducationalResource',
        name: content.title,
        description: `${content.description} في ${subjectName || 'المادة'}`,
        educationalLevel: levelName,
        about: {
          '@type': 'Course',
          name: subjectName,
          courseCode: yearName
        },
        keywords: content.keywords.join(', '),
        inLanguage: 'ar',
        dateModified: new Date().toISOString(),
        publisher: {
          '@type': 'Organization',
          name: 'منصة التعليم العربي'
        }
      }
    };
  }, [contentType, lessonTitle, subjectName, yearName, levelName]);

  return (
    <>
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(contentData.structuredData)
        }}
      />
      
      {/* Dynamic Content Header */}
      <div className="mb-6 p-6 bg-gradient-to-r from-primary/5 to-primary/10 dark:from-primary/10 dark:to-primary/20 rounded-lg border border-primary/20 dark:border-primary/30">
        <div className="mb-4">
          <h1 className="text-xl font-bold text-primary mb-2">
            {contentData.title}
          </h1>
          <p className="text-foreground/80 leading-relaxed">
            {contentData.fullText}
          </p>
        </div>

        {/* Educational Context Tags */}
        <div className="flex flex-wrap gap-2 text-sm">
          {subjectName && (
            <span className="px-3 py-1 bg-primary text-white rounded-full">
              {subjectName}
            </span>
          )}
          {yearName && (
            <span className="px-3 py-1 bg-primary text-white rounded-full">
              {yearName}
            </span>
          )}
          {levelName && (
            <span className="px-3 py-1 bg-primary text-white rounded-full">
              {levelName}
            </span>
          )}
        </div>

        {/* Keywords for SEO (hidden visually but present for SEO) */}
        <div className="sr-only">
          <span>الكلمات المفتاحية: </span>
          {contentData.keywords.join(' • ')}
        </div>
      </div>
    </>
  );
};

export default DynamicContentHeader;
